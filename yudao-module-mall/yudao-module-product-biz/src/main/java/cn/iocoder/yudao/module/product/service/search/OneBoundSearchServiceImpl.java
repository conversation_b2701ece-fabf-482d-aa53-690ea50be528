package cn.iocoder.yudao.module.product.service.search;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchDetailReqVO;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchPageReqVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuRespVO;
import cn.iocoder.yudao.module.product.framework.onebound.convert.OneBoundConvert;
import cn.iocoder.yudao.module.product.framework.onebound.core.OneBoundClient;
import cn.iocoder.yudao.module.product.framework.onebound.dto.OneBoundDetailReqDTO;
import cn.iocoder.yudao.module.product.framework.onebound.dto.OneBoundDetailRespDTO;
import cn.iocoder.yudao.module.product.framework.onebound.dto.OneBoundSearchReqDTO;
import cn.iocoder.yudao.module.product.framework.onebound.dto.OneBoundSearchRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLanguage;
import static cn.iocoder.yudao.module.product.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.product.dal.redis.RedisKeyConstants.*;

/**
 * OneBound商品搜索服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OneBoundSearchServiceImpl implements ProductSearchService {

    @Resource
    private OneBoundClient oneBoundClient;

    @Override
    @Cacheable(cacheNames = PRODUCT_SEARCH_KEYWORD + "#1800000",
            key = "{#reqVO.keyword, #reqVO.platform, #reqVO.pageNo, #reqVO.pageSize, #reqVO.sort, #reqVO.priceMin, #reqVO.priceMax}",
            unless = "#result == null || #result.list == null || #result.list.isEmpty()")
    public PageResult<AppProductSpuRespVO> searchProducts(AppSearchPageReqVO reqVO) {
        log.info("[searchProducts] 开始处理OneBound搜索请求，参数: {}", JsonUtils.toJsonString(reqVO));

        try {
            // 转换请求参数
            OneBoundSearchReqDTO searchReq = OneBoundConvert.INSTANCE.convertSearchReq(reqVO);

            // 根据平台调用不同的API
            OneBoundSearchRespDTO respDTO;
            if ("jd".equalsIgnoreCase(reqVO.getPlatform())) {
                respDTO = oneBoundClient.searchJdProducts(searchReq);
            } else {
                // 默认使用淘宝搜索
                respDTO = oneBoundClient.searchTaobaoProducts(searchReq);
            }

            // 转换响应数据
            PageResult<AppProductSpuRespVO> result = OneBoundConvert.INSTANCE.convertSearchResp(respDTO, reqVO.getPlatform());
            log.info("[searchProducts] OneBound搜索完成，返回结果: 总数={}, 当前页数据量={}",
                    result.getTotal(), result.getList() != null ? result.getList().size() : 0);

            return result;

        } catch (Exception e) {
            log.error("[searchProducts] OneBound搜索异常，异常类型: {}, 异常信息: {}", e.getClass().getSimpleName(), e.getMessage(), e);
            throw exception(ONEBOUND_SEARCH_FAILED, e.getMessage());
        }
    }

    @Override
    public AppProductSpuDetailRespVO getProductByUrl(String url) {
        log.info("[getProductByUrl] 根据URL获取商品详情，URL: {}", url);

        try {
            // 解析URL获取商品ID和平台
            ProductUrlInfo urlInfo = parseProductUrl(url);
            if (urlInfo == null) {
                throw exception(CRAWLER_SEARCH_URL_ERROR);
            }

            return getProductDetailBySourceId(urlInfo.getProductId(), urlInfo.getPlatform());

        } catch (Exception e) {
            log.error("[getProductByUrl] 根据URL获取商品详情异常", e);
            throw exception(ONEBOUND_DETAIL_FAILED, e.getMessage());
        }
    }

    @Override
    public AppProductSpuDetailRespVO getProductDetailById(AppSearchDetailReqVO reqVO) {
        log.info("[getProductDetailById] 根据ID获取商品详情，请求参数: {}", JsonUtils.toJsonString(reqVO));

        return getProductDetailBySourceId(reqVO.getId(), reqVO.getPlatform());
    }

    @Override
    @Cacheable(cacheNames = PRODUCT_SEARCH_DETAIL + "#3600000",
            key = "{#sourceId, #source}",
            unless = "#result == null")
    public AppProductSpuDetailRespVO getProductDetailBySourceId(String sourceId, String source) {
        log.info("[getProductDetailBySourceId] 根据来源ID获取商品详情，sourceId: {}, source: {}", sourceId, source);

        try {
            // 构建请求参数
            OneBoundDetailReqDTO detailReq = OneBoundConvert.INSTANCE.convertDetailReq(sourceId, getApiInvokeLanguage());

            // 根据平台调用不同的API
            OneBoundDetailRespDTO respDTO;
            if ("jd".equalsIgnoreCase(source)) {
                respDTO = oneBoundClient.getJdProductDetail(detailReq);
            } else {
                // 默认使用淘宝详情
                respDTO = oneBoundClient.getTaobaoProductDetail(detailReq);
            }

            // 转换响应数据
            AppProductSpuDetailRespVO result = OneBoundConvert.INSTANCE.convertProductDetail(respDTO, source);
            log.info("[getProductDetailBySourceId] OneBound获取详情完成，商品ID: {}", sourceId);

            return result;

        } catch (Exception e) {
            log.error("[getProductDetailBySourceId] OneBound获取详情异常", e);
            throw exception(ONEBOUND_DETAIL_FAILED, e.getMessage());
        }
    }

    /**
     * 解析商品URL获取商品ID和平台信息
     */
    private ProductUrlInfo parseProductUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return null;
        }

        // 淘宝URL解析
        if (url.contains("taobao.com") || url.contains("tmall.com")) {
            Pattern pattern = Pattern.compile("id=(\\d+)");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new ProductUrlInfo(matcher.group(1), "taobao");
            }
        }

        // 京东URL解析
        if (url.contains("jd.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new ProductUrlInfo(matcher.group(1), "jd");
            }
        }

        return null;
    }

    /**
     * 商品URL信息
     */
    private static class ProductUrlInfo {
        private final String productId;
        private final String platform;

        public ProductUrlInfo(String productId, String platform) {
            this.productId = productId;
            this.platform = platform;
        }

        public String getProductId() {
            return productId;
        }

        public String getPlatform() {
            return platform;
        }
    }


    private static String  getApiInvokeLanguage() {
        String language = StrUtil.isNotEmpty(getCurrentLanguage().getLanguage())? getCurrentLanguage().getLanguage() : "en" ;
        switch ( language) {
            case "zh":
                return "cn";
            case "fr":
                return "fr";
            case "de":
                return "de";
            case "es":
                return "es";
            case "ar":
                return "ar";
            case "en":
            default:
                return "en";
        }
    }

}
