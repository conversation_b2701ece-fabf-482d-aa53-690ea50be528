# 支付密码功能测试用例

## 测试环境准备

1. 确保用户已注册并登录
2. 确保用户手机号已绑定
3. 确保短信服务正常工作
4. 准备测试用的支付订单

## 测试用例

### 1. 设置支付密码测试

#### 测试用例1.1: 正常设置支付密码
**前置条件**: 用户未设置过支付密码
**测试步骤**:
1. 发送短信验证码到用户手机
2. 调用设置支付密码接口
```bash
curl -X PUT "http://localhost:8080/member/user/set-pay-password" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "payPassword": "123456",
    "code": "1234"
  }'
```
**预期结果**: 返回成功，支付密码设置成功

#### 测试用例1.2: 重复设置支付密码
**前置条件**: 用户已设置过支付密码
**测试步骤**: 同1.1
**预期结果**: 返回错误码 `1_004_001_008`，提示新支付密码不能与旧密码相同

#### 测试用例1.3: 验证码错误
**前置条件**: 用户未设置过支付密码
**测试步骤**: 使用错误的验证码调用接口
**预期结果**: 返回验证码错误

### 2. 修改支付密码测试

#### 测试用例2.1: 正常修改支付密码
**前置条件**: 用户已设置支付密码
**测试步骤**:
```bash
curl -X PUT "http://localhost:8080/member/user/update-pay-password" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "oldPayPassword": "123456",
    "newPayPassword": "654321"
  }'
```
**预期结果**: 返回成功，支付密码修改成功

#### 测试用例2.2: 旧密码错误
**前置条件**: 用户已设置支付密码
**测试步骤**: 使用错误的旧密码调用接口
**预期结果**: 返回错误码 `1_004_001_007`，提示支付密码错误

#### 测试用例2.3: 新旧密码相同
**前置条件**: 用户已设置支付密码
**测试步骤**: 新密码与旧密码相同
**预期结果**: 返回错误码 `1_004_001_008`，提示新支付密码不能与旧密码相同

#### 测试用例2.4: 未设置支付密码
**前置条件**: 用户未设置支付密码
**测试步骤**: 调用修改支付密码接口
**预期结果**: 返回错误码 `1_004_001_006`，提示支付密码未设置

### 3. 重置支付密码测试

#### 测试用例3.1: 正常重置支付密码
**前置条件**: 用户已注册
**测试步骤**:
1. 发送短信验证码
2. 调用重置支付密码接口
```bash
curl -X PUT "http://localhost:8080/member/user/reset-pay-password" \
  -H "Content-Type: application/json" \
  -d '{
    "mobile": "15601691300",
    "code": "1234",
    "payPassword": "123456"
  }'
```
**预期结果**: 返回成功，支付密码重置成功

#### 测试用例3.2: 手机号未注册
**前置条件**: 使用未注册的手机号
**测试步骤**: 使用未注册手机号调用接口
**预期结果**: 返回错误码 `1_004_001_001`，提示手机号未注册用户

### 4. 验证支付密码测试

#### 测试用例4.1: 正确的支付密码
**前置条件**: 用户已设置支付密码
**测试步骤**:
```bash
curl -X POST "http://localhost:8080/member/user/verify-pay-password" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "payPassword": "123456"
  }'
```
**预期结果**: 返回 `data: true`

#### 测试用例4.2: 错误的支付密码
**前置条件**: 用户已设置支付密码
**测试步骤**: 使用错误的支付密码
**预期结果**: 返回 `data: false`

#### 测试用例4.3: 未设置支付密码
**前置条件**: 用户未设置支付密码
**测试步骤**: 调用验证接口
**预期结果**: 返回错误码 `1_004_001_006`，提示支付密码未设置

### 5. 余额支付测试

#### 测试用例5.1: 正常余额支付
**前置条件**: 用户已设置支付密码，账户有足够余额
**测试步骤**:
```bash
curl -X POST "http://localhost:8080/pay/order/submit" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1024,
    "channelCode": "wallet",
    "payPassword": "123456"
  }'
```
**预期结果**: 返回成功，支付处理成功

#### 测试用例5.2: 余额支付未提供支付密码
**前置条件**: 用户已设置支付密码
**测试步骤**: 不传递payPassword参数
**预期结果**: 返回错误码 `1_007_007_005`，提示使用余额支付需要验证支付密码

#### 测试用例5.3: 余额支付密码错误
**前置条件**: 用户已设置支付密码
**测试步骤**: 传递错误的支付密码
**预期结果**: 返回错误码 `1_007_007_005`，提示使用余额支付需要验证支付密码

#### 测试用例5.4: 其他支付方式不需要支付密码
**前置条件**: 用户选择非余额支付方式
**测试步骤**:
```bash
curl -X POST "http://localhost:8080/pay/order/submit" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1024,
    "channelCode": "alipay_pc"
  }'
```
**预期结果**: 返回成功，不需要验证支付密码

## 边界条件测试

### 密码长度测试
- 测试5位密码（应该失败）
- 测试6位密码（应该成功）
- 测试20位密码（应该成功）
- 测试21位密码（应该失败）

### 验证码测试
- 测试3位验证码（应该失败）
- 测试4位验证码（应该成功）
- 测试6位验证码（应该成功）
- 测试7位验证码（应该失败）

### 手机号格式测试
- 测试正确格式手机号（应该成功）
- 测试错误格式手机号（应该失败）

## 安全性测试

### 密码加密测试
1. 设置支付密码后，检查数据库中密码是否已加密
2. 验证BCrypt加密是否正确工作

### 并发测试
1. 同时多次调用设置支付密码接口
2. 同时多次调用修改支付密码接口
3. 验证数据一致性

### 权限测试
1. 未登录用户调用需要认证的接口（应该失败）
2. 登录用户调用其他用户的支付密码相关接口（应该失败）

## 性能测试

### 响应时间测试
- 各接口响应时间应在合理范围内（建议<500ms）

### 压力测试
- 高并发情况下接口稳定性测试

## 回归测试

在每次代码修改后，重新执行所有测试用例，确保功能正常。
