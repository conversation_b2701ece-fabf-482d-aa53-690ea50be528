package cn.iocoder.yudao.module.member.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class AppMemberAutoCompensateDiffReqVO {


    @Schema(description = "是否自动补款", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "补款授权不能为空")
    private Boolean autoCompensateDiff;
}
