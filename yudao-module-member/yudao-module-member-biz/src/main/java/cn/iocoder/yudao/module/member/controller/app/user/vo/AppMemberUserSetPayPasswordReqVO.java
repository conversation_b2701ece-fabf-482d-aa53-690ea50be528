package cn.iocoder.yudao.module.member.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

@Schema(description = "用户 APP - 设置支付密码 Request VO")
@Data
public class AppMemberUserSetPayPasswordReqVO {

    @Schema(description = "支付密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotEmpty(message = "支付密码不能为空")
    @Length(min = 6, max = 20, message = "支付密码长度为 6-20 位")
    private String payPassword;

//    @Schema(description = "短信验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
//    @NotEmpty(message = "验证码不能为空")
//    @Length(min = 4, max = 6, message = "验证码长度为 4-6 位")
//    private String code;

}
