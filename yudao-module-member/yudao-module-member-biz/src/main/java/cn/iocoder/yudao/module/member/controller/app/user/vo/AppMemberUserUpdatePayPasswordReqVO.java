package cn.iocoder.yudao.module.member.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

@Schema(description = "用户 APP - 修改支付密码 Request VO")
@Data
public class AppMemberUserUpdatePayPasswordReqVO {

    @Schema(description = "旧支付密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotEmpty(message = "旧支付密码不能为空")
    @Length(min = 6, max = 20, message = "旧支付密码长度为 6-20 位")
    private String oldPayPassword;

    @Schema(description = "新支付密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "654321")
    @NotEmpty(message = "新支付密码不能为空")
    @Length(min = 6, max = 20, message = "新支付密码长度为 6-20 位")
    private String newPayPassword;

}
